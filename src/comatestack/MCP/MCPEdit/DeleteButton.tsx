import {Button, message, Modal} from '@panda-design/components';
import {useCallback} from 'react';
import {useNavigate} from 'react-router-dom';
import {IconAlert} from '@/icons/mcp';
import {useMCPServerId, useMCPWorkspaceId} from '@/components/MCP/hooks';
import {apiDeleteServer} from '@/api/mcp';
import {MCPSpaceLink} from '@/links/mcp';


const DeleteButton = () => {
    const mcpId = useMCPServerId();
    const navigate = useNavigate();
    const workspaceId = useMCPWorkspaceId();
    const onClick = useCallback(
        () => {
            Modal.confirm({
                content: 'MCP Server删除后不可恢复，请谨慎操作。确定删除当前MCP Server吗？',
                icon: <IconAlert />,
                onOk: async () => {
                    await apiDeleteServer({mcpServerId: mcpId});
                    message.success('删除成功');
                    navigate(MCPSpaceLink.toUrl({workspaceId}));
                },
            });
        },
        [mcpId, navigate, workspaceId]
    );
    return (
        <Button onClick={onClick} type="text">删除</Button>
    );
};

export default DeleteButton;

